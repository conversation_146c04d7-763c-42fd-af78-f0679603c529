# RTOS学习笔记4 - 中断处理

## 1. RTOS中断概念

### 基本概念
在RTOS环境中，中断处理需要特别注意与任务调度器的协调。中断具有比任务更高的优先级，但需要遵循特定的规则。
- **硬件优先级** - 中断优先级高于所有任务
- **嵌套支持** - 支持中断嵌套（高优先级中断可以打断低优先级中断）
- **上下文切换** - 中断可能触发任务切换
- **API限制** - 中断中只能使用特定的API

### 中断优先级分组
STM32使用NVIC管理中断，支持中断优先级分组：
- **抢占优先级** - 决定中断嵌套关系
- **响应优先级** - 同抢占优先级内的执行顺序
- **FreeRTOS临界区** - 某些优先级的中断会被屏蔽

### 关键配置参数
在FreeRTOSConfig.h中的重要配置：
```c
// 系统可管理的最高中断优先级
#define configLIBRARY_LOWEST_INTERRUPT_PRIORITY     15

// 系统可管理的最低中断优先级  
#define configLIBRARY_MAX_SYSCALL_INTERRUPT_PRIORITY 5

// 内核中断优先级
#define configKERNEL_INTERRUPT_PRIORITY             (configLIBRARY_LOWEST_INTERRUPT_PRIORITY << 4)
```

## 2. 中断优先级规则

### 优先级分类
中断按优先级分为三类：

#### 1. 超高优先级中断 (0-4)
- **特点**：不受FreeRTOS管理，不会被屏蔽
- **限制**：不能调用任何FreeRTOS API
- **用途**：极其紧急的硬件处理
```c
// 超高优先级中断 - 不能调用FreeRTOS API
void EXTI0_IRQHandler(void) {
    // 只能做最基本的硬件操作
    HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
    // 不能调用 xQueueSendFromISR() 等API
}
```

#### 2. 可管理中断 (5-15)
- **特点**：受FreeRTOS管理，可被临界区屏蔽
- **权限**：可以调用FromISR版本的API
- **用途**：大部分应用中断
```c
// 可管理中断 - 可以调用FromISR API
void USART1_IRQHandler(void) {
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    
    if (__HAL_UART_GET_FLAG(&huart1, UART_FLAG_RXNE)) {
        uint8_t data = huart1.Instance->DR;
        xQueueSendFromISR(xUartQueue, &data, &xHigherPriorityTaskWoken);
    }
    
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}
```

#### 3. 系统中断
- **SysTick**：系统时钟中断，优先级通常设为最低
- **PendSV**：任务切换中断
- **SVCall**：系统调用中断

### CubeMX中断优先级配置
在STM32CubeMX中配置路径：**System Core → NVIC**
- **Preemption Priority**: 0-15 (抢占优先级)
- **Sub Priority**: 0-15 (响应优先级)
- **建议**：FreeRTOS相关中断设置为5-15

## 3. 中断服务程序 (ISR) 编写规则

### FromISR API规则
中断中只能使用带FromISR后缀的API：
```c
// 正确的中断API使用
xQueueSendFromISR(queue, &data, &xHigherPriorityTaskWoken);
xSemaphoreGiveFromISR(semaphore, &xHigherPriorityTaskWoken);
vTaskNotifyGiveFromISR(taskHandle, &xHigherPriorityTaskWoken);
xTimerStartFromISR(timer, &xHigherPriorityTaskWoken);

// 错误 - 不能在中断中使用
xQueueSend(queue, &data, timeout);        // ❌
xSemaphoreTake(semaphore, timeout);       // ❌
vTaskDelay(100);                          // ❌
```

### 任务切换处理
```c
void EXTI1_IRQHandler(void) {
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    
    // 处理中断
    if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_1) == GPIO_PIN_SET) {
        // 通知任务
        vTaskNotifyGiveFromISR(xTaskHandle, &xHigherPriorityTaskWoken);
    }
    
    // 清除中断标志
    __HAL_GPIO_EXTI_CLEAR_IT(GPIO_PIN_1);
    
    // 如果有更高优先级任务被唤醒，进行任务切换
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}
```

### ISR编写最佳实践
1. **保持简短** - 中断处理时间越短越好
2. **延迟处理** - 复杂处理交给任务完成
3. **避免阻塞** - 不使用会阻塞的操作
4. **正确清标志** - 及时清除中断标志位

## 4. 中断与任务协作模式

### 1. 中断通知任务模式
```c
TaskHandle_t xProcessTaskHandle;

// 中断服务程序
void TIM2_IRQHandler(void) {
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    
    if (__HAL_TIM_GET_FLAG(&htim2, TIM_FLAG_UPDATE)) {
        // 通知处理任务
        vTaskNotifyGiveFromISR(xProcessTaskHandle, &xHigherPriorityTaskWoken);
        __HAL_TIM_CLEAR_FLAG(&htim2, TIM_FLAG_UPDATE);
    }
    
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}

// 处理任务
void ProcessTask(void *pvParameters) {
    for (;;) {
        // 等待中断通知
        ulTaskNotifyTake(pdTRUE, portMAX_DELAY);
        
        // 执行复杂的处理逻辑
        ComplexDataProcessing();
    }
}
```

### 2. 中断数据传递模式
```c
QueueHandle_t xDataQueue;

// ADC中断 - 传递采集数据
void ADC1_IRQHandler(void) {
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    uint16_t adcValue;
    
    if (__HAL_ADC_GET_FLAG(&hadc1, ADC_FLAG_EOC)) {
        adcValue = HAL_ADC_GetValue(&hadc1);
        
        // 发送数据到队列
        xQueueSendFromISR(xDataQueue, &adcValue, &xHigherPriorityTaskWoken);
        
        __HAL_ADC_CLEAR_FLAG(&hadc1, ADC_FLAG_EOC);
    }
    
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}
```

### 3. 中断事件标志模式
```c
EventGroupHandle_t xEventGroup;
#define DATA_READY_BIT    (1 << 0)
#define ERROR_BIT         (1 << 1)

// UART中断 - 设置事件标志
void USART2_IRQHandler(void) {
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    
    if (__HAL_UART_GET_FLAG(&huart2, UART_FLAG_RXNE)) {
        // 数据接收完成
        xEventGroupSetBitsFromISR(xEventGroup, DATA_READY_BIT, &xHigherPriorityTaskWoken);
    }
    
    if (__HAL_UART_GET_FLAG(&huart2, UART_FLAG_ORE)) {
        // 溢出错误
        xEventGroupSetBitsFromISR(xEventGroup, ERROR_BIT, &xHigherPriorityTaskWoken);
        __HAL_UART_CLEAR_OREFLAG(&huart2);
    }
    
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}
```

## 5. 临界区保护

### 什么是临界区
临界区是不能被中断打断的代码段，用于保护共享资源。

### 临界区API
```c
// 进入临界区 - 禁用中断
taskENTER_CRITICAL();
// 访问共享资源
shared_variable++;
// 退出临界区 - 恢复中断
taskEXIT_CRITICAL();

// 中断中的临界区
UBaseType_t uxSavedInterruptStatus;
uxSavedInterruptStatus = taskENTER_CRITICAL_FROM_ISR();
// 访问共享资源
shared_variable++;
taskEXIT_CRITICAL_FROM_ISR(uxSavedInterruptStatus);
```

### 临界区使用注意事项
1. **保持简短** - 临界区时间越短越好
2. **避免嵌套** - 尽量避免临界区嵌套
3. **不要阻塞** - 临界区内不要调用阻塞函数
4. **成对使用** - 确保每个ENTER都有对应的EXIT

## 6. 中断调试技巧

### 1. 中断计数器
```c
volatile uint32_t interrupt_count = 0;

void EXTI2_IRQHandler(void) {
    interrupt_count++;  // 统计中断次数
    
    // 正常中断处理
    // ...
}
```

### 2. 中断执行时间测量
```c
void TIM3_IRQHandler(void) {
    uint32_t start_time = DWT->CYCCNT;  // 开始时间
    
    // 中断处理代码
    ProcessInterrupt();
    
    uint32_t end_time = DWT->CYCCNT;    // 结束时间
    uint32_t cycles = end_time - start_time;
    
    // 记录最大执行时间
    if (cycles > max_isr_cycles) {
        max_isr_cycles = cycles;
    }
}
```

### 3. 中断嵌套检测
```c
volatile uint8_t isr_nesting_level = 0;

void Generic_IRQHandler(void) {
    isr_nesting_level++;
    
    // 检测嵌套深度
    if (isr_nesting_level > max_nesting_level) {
        max_nesting_level = isr_nesting_level;
    }
    
    // 中断处理
    // ...
    
    isr_nesting_level--;
}
```

## 7. 常见中断问题

### 1. 中断优先级设置错误
- **问题**：中断优先级设置在0-4范围内
- **后果**：无法调用FreeRTOS API
- **解决**：设置为5-15范围内

### 2. 忘记调用portYIELD_FROM_ISR
- **问题**：中断唤醒任务后不切换
- **后果**：任务响应延迟
- **解决**：正确使用portYIELD_FROM_ISR

### 3. 中断处理时间过长
- **问题**：在中断中执行复杂逻辑
- **后果**：影响系统实时性
- **解决**：使用中断通知任务模式

### 4. 临界区使用不当
- **问题**：临界区时间过长或嵌套
- **后果**：影响中断响应
- **解决**：保持临界区简短，避免嵌套
