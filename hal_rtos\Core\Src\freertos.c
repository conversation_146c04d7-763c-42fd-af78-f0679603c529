/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */

/* USER CODE END Variables */
osThreadId MonitorTaskHandle;
osThreadId DisplayTaskHandle;
osThreadId LedControlTaskHandle;
osThreadId KeyTaskHandle;
osThreadId DemoControlTaskHandle;
osMessageQId KeyEventQueueHandle;
osMessageQId DisplayDataQueueHandle;
osTimerId LedTimerHandle;
osTimerId BeepTimerHandle;
osTimerId WatchdogTimerHandle;
osTimerId DisplayRefreshTimerHandle;
osMutexId DisplayMutexHandle;
osMutexId SystemMutexHandle;
osSemaphoreId KeySemaphoreHandle;
osSemaphoreId ResourceSemaphoreHandle;

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */

/* USER CODE END FunctionPrototypes */

void StartMonitorTask(void const * argument);
void StartDisplayTask(void const * argument);
void StartLedControlTask(void const * argument);
void StartKeyTask(void const * argument);
void StartDemoControlTask(void const * argument);
void LedTimerCallback(void const * argument);
void BeepTimerCallback(void const * argument);
void WatchdogTimerCallback(void const * argument);
void DisplayRefreshCallback(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* GetTimerTaskMemory prototype (linked to static allocation support) */
void vApplicationGetTimerTaskMemory( StaticTask_t **ppxTimerTaskTCBBuffer, StackType_t **ppxTimerTaskStackBuffer, uint32_t *pulTimerTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];

void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
  /* place for user code */
}
/* USER CODE END GET_IDLE_TASK_MEMORY */

/* USER CODE BEGIN GET_TIMER_TASK_MEMORY */
static StaticTask_t xTimerTaskTCBBuffer;
static StackType_t xTimerStack[configTIMER_TASK_STACK_DEPTH];

void vApplicationGetTimerTaskMemory( StaticTask_t **ppxTimerTaskTCBBuffer, StackType_t **ppxTimerTaskStackBuffer, uint32_t *pulTimerTaskStackSize )
{
  *ppxTimerTaskTCBBuffer = &xTimerTaskTCBBuffer;
  *ppxTimerTaskStackBuffer = &xTimerStack[0];
  *pulTimerTaskStackSize = configTIMER_TASK_STACK_DEPTH;
  /* place for user code */
}
/* USER CODE END GET_TIMER_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */
  /* Create the mutex(es) */
  /* definition and creation of DisplayMutex */
  osMutexDef(DisplayMutex);
  DisplayMutexHandle = osMutexCreate(osMutex(DisplayMutex));

  /* definition and creation of SystemMutex */
  osMutexDef(SystemMutex);
  SystemMutexHandle = osMutexCreate(osMutex(SystemMutex));

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* Create the semaphores(s) */
  /* definition and creation of KeySemaphore */
  osSemaphoreDef(KeySemaphore);
  KeySemaphoreHandle = osSemaphoreCreate(osSemaphore(KeySemaphore), 1);

  /* definition and creation of ResourceSemaphore */
  osSemaphoreDef(ResourceSemaphore);
  ResourceSemaphoreHandle = osSemaphoreCreate(osSemaphore(ResourceSemaphore), 3);

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* Create the timer(s) */
  /* definition and creation of LedTimer */
  osTimerDef(LedTimer, LedTimerCallback);
  LedTimerHandle = osTimerCreate(osTimer(LedTimer), osTimerPeriodic, NULL);

  /* definition and creation of BeepTimer */
  osTimerDef(BeepTimer, BeepTimerCallback);
  BeepTimerHandle = osTimerCreate(osTimer(BeepTimer), osTimerOnce, NULL);

  /* definition and creation of WatchdogTimer */
  osTimerDef(WatchdogTimer, WatchdogTimerCallback);
  WatchdogTimerHandle = osTimerCreate(osTimer(WatchdogTimer), osTimerPeriodic, NULL);

  /* definition and creation of DisplayRefreshTimer */
  osTimerDef(DisplayRefreshTimer, DisplayRefreshCallback);
  DisplayRefreshTimerHandle = osTimerCreate(osTimer(DisplayRefreshTimer), osTimerPeriodic, NULL);

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* Create the queue(s) */
  /* definition and creation of KeyEventQueue */
  osMessageQDef(KeyEventQueue, 10, uint32_t);
  KeyEventQueueHandle = osMessageCreate(osMessageQ(KeyEventQueue), NULL);

  /* definition and creation of DisplayDataQueue */
  osMessageQDef(DisplayDataQueue, 5, uint64_t);
  DisplayDataQueueHandle = osMessageCreate(osMessageQ(DisplayDataQueue), NULL);

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of MonitorTask */
  osThreadDef(MonitorTask, StartMonitorTask, osPriorityLow, 0, 256);
  MonitorTaskHandle = osThreadCreate(osThread(MonitorTask), NULL);

  /* definition and creation of DisplayTask */
  osThreadDef(DisplayTask, StartDisplayTask, osPriorityAboveNormal, 0, 256);
  DisplayTaskHandle = osThreadCreate(osThread(DisplayTask), NULL);

  /* definition and creation of LedControlTask */
  osThreadDef(LedControlTask, StartLedControlTask, osPriorityNormal, 0, 128);
  LedControlTaskHandle = osThreadCreate(osThread(LedControlTask), NULL);

  /* definition and creation of KeyTask */
  osThreadDef(KeyTask, StartKeyTask, osPriorityBelowNormal, 0, 128);
  KeyTaskHandle = osThreadCreate(osThread(KeyTask), NULL);

  /* definition and creation of DemoControlTask */
  osThreadDef(DemoControlTask, StartDemoControlTask, osPriorityHigh, 0, 256);
  DemoControlTaskHandle = osThreadCreate(osThread(DemoControlTask), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartMonitorTask */
/**
  * @brief  Function implementing the MonitorTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartMonitorTask */
void StartMonitorTask(void const * argument)
{
  /* USER CODE BEGIN StartMonitorTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartMonitorTask */
}

/* USER CODE BEGIN Header_StartDisplayTask */
/**
* @brief Function implementing the DisplayTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartDisplayTask */
void StartDisplayTask(void const * argument)
{
  /* USER CODE BEGIN StartDisplayTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartDisplayTask */
}

/* USER CODE BEGIN Header_StartLedControlTask */
/**
* @brief Function implementing the LedControlTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartLedControlTask */
void StartLedControlTask(void const * argument)
{
  /* USER CODE BEGIN StartLedControlTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartLedControlTask */
}

/* USER CODE BEGIN Header_StartKeyTask */
/**
* @brief Function implementing the KeyTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartKeyTask */
void StartKeyTask(void const * argument)
{
  /* USER CODE BEGIN StartKeyTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartKeyTask */
}

/* USER CODE BEGIN Header_StartDemoControlTask */
/**
* @brief Function implementing the DemoControlTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartDemoControlTask */
void StartDemoControlTask(void const * argument)
{
  /* USER CODE BEGIN StartDemoControlTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartDemoControlTask */
}

/* LedTimerCallback function */
void LedTimerCallback(void const * argument)
{
  /* USER CODE BEGIN LedTimerCallback */

  /* USER CODE END LedTimerCallback */
}

/* BeepTimerCallback function */
void BeepTimerCallback(void const * argument)
{
  /* USER CODE BEGIN BeepTimerCallback */

  /* USER CODE END BeepTimerCallback */
}

/* WatchdogTimerCallback function */
void WatchdogTimerCallback(void const * argument)
{
  /* USER CODE BEGIN WatchdogTimerCallback */

  /* USER CODE END WatchdogTimerCallback */
}

/* DisplayRefreshCallback function */
void DisplayRefreshCallback(void const * argument)
{
  /* USER CODE BEGIN DisplayRefreshCallback */

  /* USER CODE END DisplayRefreshCallback */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

/* USER CODE END Application */

